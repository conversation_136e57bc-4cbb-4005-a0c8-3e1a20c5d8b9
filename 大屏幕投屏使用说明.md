# 大屏幕投屏使用说明

## 屏幕规格
- **大屏幕物理尺寸**: 3200*1000px (3.2:1比例)
- **电脑分辨率设置**: 3200*1000px
- **像素密度**: 72px
- **连接方式**: HDMI接口投屏

## 重要说明
**投屏原理**: HDMI投屏会将电脑屏幕内容直接投射到大屏幕上，所以需要：
1. 将电脑显示分辨率设置为3200*1000px
2. 通过HDMI连接投屏到大屏幕
3. 页面会自动检测并适配3200*1000分辨率

## 页面访问
访问路径: `http://your-server:8000/extract2`

## 优化特性

### 1. 分辨率检测优化
- 自动检测当前浏览器窗口分辨率
- 专门针对3200*1000px分辨率优化
- 确保页面内容不会变形或扭曲

### 2. 布局优化
- **主标题**: 适配大屏幕尺寸，避免过度拉伸
- **抽取结果区域**: 增大容器尺寸，提升可读性
- **倒计时器**: 放大显示，便于远距离观看
- **操作按钮**: 增大尺寸，提升交互体验

### 3. 字体优化
- 所有文字大小针对大屏幕进行了放大
- 保持良好的可读性和视觉层次
- 适合远距离观看

### 4. 背景图片优化
- 使用`background-size: cover`确保背景图片完整覆盖
- `background-position: center center`保持居中显示
- 避免图片拉伸变形

## 使用步骤

### 1. 启动服务
```bash
# 激活虚拟环境
source myenv/bin/activate

# 启动FastAPI服务
python -m uvicorn main:app --host 0.0.0.0 --port 8000
```

### 2. 浏览器设置
1. 打开Chrome或其他现代浏览器
2. 访问 `http://localhost:8000/extract2`
3. 按F11进入全屏模式
4. 通过HDMI连接到大屏幕设备

### 3. 投屏设置步骤

#### 步骤1: 设置电脑分辨率
1. **Windows系统**:
   - 右键桌面 → 显示设置
   - 将分辨率设置为3200×1000
   - 如果没有此选项，需要在显卡控制面板中添加自定义分辨率

2. **macOS系统**:
   - 系统偏好设置 → 显示器
   - 按住Option键点击"缩放"
   - 选择或添加3200×1000分辨率

#### 步骤2: HDMI连接投屏
1. 使用HDMI线连接电脑和大屏幕
2. 设置显示模式为"复制显示"（推荐）
3. 确保大屏幕显示电脑桌面内容

#### 步骤3: 浏览器设置
1. 打开浏览器访问 `http://localhost:8000/extract2`
2. 按F11进入全屏模式
3. 页面会自动检测3200×1000分辨率并应用优化

## 技术特性

### CSS媒体查询
```css
/* 3200*1000px分辨率精确优化 */
@media (min-width: 3200px) and (max-width: 3200px) and
       (min-height: 1000px) and (max-height: 1000px) {
    /* 针对3200*1000分辨率的专门优化 */
    .result-container {
        width: 1600px; /* 50% of screen width */
        height: 700px;
    }
    .result-item {
        font-size: 64px; /* 大字体适应超宽屏 */
    }
}
```

### JavaScript优化
- 自动检测屏幕尺寸
- 动态应用大屏幕优化
- 监听窗口大小变化
- 全屏状态优化

## 注意事项

1. **浏览器兼容性**: 建议使用Chrome、Firefox或Edge最新版本
2. **硬件要求**: 确保显卡支持3200*1000分辨率输出
3. **网络连接**: 确保网络稳定，避免页面加载问题
4. **图片资源**: 确保`/static/img/`目录下的图片资源完整

## 故障排除

### 分辨率设置问题

#### Windows系统分辨率设置
1. **找不到3200×1000分辨率选项**:
   - 打开NVIDIA控制面板或AMD显卡设置
   - 选择"更改分辨率" → "自定义"
   - 添加自定义分辨率：3200×1000，刷新率60Hz
   - 应用设置

2. **显卡不支持该分辨率**:
   - 检查显卡驱动是否为最新版本
   - 确认HDMI线材支持高分辨率传输
   - 尝试使用DisplayPort连接（如果可用）

#### macOS系统分辨率设置
1. **系统偏好设置中没有该选项**:
   - 下载并安装SwitchResX等第三方分辨率工具
   - 或使用终端命令添加自定义分辨率
   - 重启系统后在显示器设置中选择

### 页面显示异常
1. **页面布局不正确**:
   - 按F12打开开发者工具，查看控制台输出
   - 确认JavaScript检测到正确的分辨率
   - 检查CSS媒体查询是否生效

2. **字体或元素过小/过大**:
   - 确认浏览器缩放比例为100%
   - 检查系统DPI设置是否为100%
   - 清除浏览器缓存并刷新页面

### 投屏显示问题
1. **HDMI连接问题**:
   - 检查HDMI线材是否支持高分辨率
   - 确认大屏幕支持3200×1000输入
   - 尝试重新插拔HDMI连接

2. **显示模式问题**:
   - 推荐使用"复制显示"模式
   - 避免使用"扩展显示"，可能导致分辨率不匹配
   - 确保主显示器设置为正确的分辨率

### 性能优化
1. 关闭不必要的浏览器标签页
2. 确保系统资源充足
3. 使用硬件加速功能
4. 定期清理浏览器缓存

## 测试和验证

### 分辨率检测测试
1. 访问页面后按F12打开开发者工具
2. 在控制台中查看输出信息：
   ```
   当前分辨率: 3200x1000, 比例: 3.20
   检测到3200*1000分辨率，应用大屏幕优化
   ```
3. 如果看到此信息，说明检测成功

### 视觉效果验证
1. **标题图片**: 应该占屏幕宽度的30%左右，不会过度拉伸
2. **抽取结果区域**: 居中显示，字体清晰可读
3. **倒计时器**: 位于右下角，数字足够大
4. **操作按钮**: 尺寸适中，便于点击

### 功能测试清单
- [ ] 页面正常加载，无错误信息
- [ ] 全屏模式下布局正确
- [ ] 点击"开始抽题"按钮响应正常
- [ ] 抽取动画流畅显示
- [ ] 倒计时功能正常工作
- [ ] 所有文字清晰可读
- [ ] 背景图片显示完整，无变形

## 技术支持

### 调试信息收集
如遇问题，请提供以下信息：
1. 操作系统版本
2. 浏览器类型和版本
3. 当前设置的分辨率
4. 控制台错误信息截图
5. 页面显示效果截图

### 联系方式
如遇到技术问题，请联系开发团队获取支持。

## 附录：快速设置指南

### Windows快速设置
```batch
# 1. 设置分辨率为3200×1000
# 2. 连接HDMI到大屏幕
# 3. 启动服务
cd /path/to/project
source myenv/bin/activate  # 如果使用Git Bash
python -m uvicorn main:app --host 0.0.0.0 --port 8000
# 4. 浏览器访问 http://localhost:8000/extract2
# 5. 按F11全屏
```

### macOS快速设置
```bash
# 1. 设置分辨率为3200×1000
# 2. 连接HDMI到大屏幕
# 3. 启动服务
cd /path/to/project
source myenv/bin/activate
python -m uvicorn main:app --host 0.0.0.0 --port 8000
# 4. 浏览器访问 http://localhost:8000/extract2
# 5. 按F11全屏
```
