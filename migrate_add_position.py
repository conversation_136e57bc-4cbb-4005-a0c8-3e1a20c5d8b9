#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database migration script: Add position field to Person table
"""

import sqlite3
import os

def migrate_database():
    """Add position field to Person table"""
    db_path = 'extraction.db'
    
    if not os.path.exists(db_path):
        print("Database file does not exist, no migration needed")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if position field already exists
        cursor.execute("PRAGMA table_info(persons)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'position' not in columns:
            print("Adding position field to persons table...")
            cursor.execute("ALTER TABLE persons ADD COLUMN position TEXT")
            conn.commit()
            print("Position field added successfully!")
        else:
            print("Position field already exists, no need to add")

        conn.close()
        print("Database migration completed")
        
    except Exception as e:
        print("Database migration failed: " + str(e))

if __name__ == "__main__":
    migrate_database()
