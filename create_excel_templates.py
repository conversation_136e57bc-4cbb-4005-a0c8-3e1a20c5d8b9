#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Excel templates for person and question import
"""

import pandas as pd

def create_person_template():
    """Create person Excel template"""
    data = {
        '姓名': ['张三', '李四', '王五', '赵六'],
        '机构': ['技术部', '市场部', '人事部', '财务部'],
        '职务': ['技术经理', '市场主管', '人事专员', '财务分析师']
    }
    df = pd.DataFrame(data)
    df.to_excel('人员模板.xlsx', index=False)
    print("人员模板已创建: 人员模板.xlsx")
    print("包含字段: 姓名、机构、职务")

def create_question_template():
    """Create question Excel template"""
    data = {
        '题目名称': ['项目管理经验分享', '市场趋势分析', '团队协作心得', '成本控制方法', '技术创新思路']
    }
    df = pd.DataFrame(data)
    df.to_excel('题目模板.xlsx', index=False)
    print("题目模板已创建: 题目模板.xlsx")
    print("包含字段: 题目名称")

if __name__ == "__main__":
    create_person_template()
    create_question_template()
    print("\nExcel模板创建完成！")
    print("使用说明：")
    print("1. 人员Excel必须包含：姓名、机构、职务 三个字段")
    print("2. 题目Excel必须包含：题目名称 一个字段")
    print("3. 上传时请确保字段名称完全一致")
