#!/usr/bin/env python3
"""
下载 Python 3.10 官方离线安装包
支持 Windows 和 Linux 平台
"""

import os
import sys
import urllib.request
import urllib.error
from pathlib import Path

PYTHON_VERSION = "3.10.16"
PACKAGE_DIR = "package"
BASE_URL = f"https://www.python.org/ftp/python/{PYTHON_VERSION}"

# Python 3.10.16 的官方下载文件列表
DOWNLOAD_FILES = {
    "windows_x64": {
        "filename": f"python-{PYTHON_VERSION}-amd64.exe",
        "description": "Windows x86-64 executable installer"
    },
    "windows_x86": {
        "filename": f"python-{PYTHON_VERSION}.exe", 
        "description": "Windows x86 executable installer"
    },
    "linux_source": {
        "filename": f"Python-{PYTHON_VERSION}.tgz",
        "description": "Linux/Unix source code (Gzipped source tarball)"
    },
    "linux_xz": {
        "filename": f"Python-{PYTHON_VERSION}.tar.xz",
        "description": "Linux/Unix source code (XZ compressed source tarball)"
    }
}

def download_file(url, filepath):
    """下载文件并显示进度"""
    try:
        print(f"正在下载: {url}")
        
        def progress_hook(block_num, block_size, total_size):
            if total_size > 0:
                percent = min(100, (block_num * block_size * 100) // total_size)
                print(f"\r进度: {percent}%", end="", flush=True)
        
        urllib.request.urlretrieve(url, filepath, progress_hook)
        print(f"\n✓ 下载完成: {filepath}")
        return True
        
    except urllib.error.URLError as e:
        print(f"\n✗ 下载失败: {e}")
        return False
    except Exception as e:
        print(f"\n✗ 下载出错: {e}")
        return False

def main():
    """主函数"""
    print(f"Python {PYTHON_VERSION} 离线安装包下载工具")
    print("=" * 50)
    
    # 创建 package 目录
    package_path = Path(PACKAGE_DIR)
    package_path.mkdir(exist_ok=True)
    
    # 显示可用的下载选项
    print("可用的下载选项:")
    for key, info in DOWNLOAD_FILES.items():
        print(f"  {key}: {info['description']}")
    
    print("\n开始下载...")
    
    success_count = 0
    total_count = len(DOWNLOAD_FILES)
    
    for platform, info in DOWNLOAD_FILES.items():
        filename = info["filename"]
        url = f"{BASE_URL}/{filename}"
        filepath = package_path / filename
        
        print(f"\n[{platform}] {info['description']}")
        
        # 检查文件是否已存在
        if filepath.exists():
            print(f"文件已存在: {filepath}")
            file_size = filepath.stat().st_size
            print(f"文件大小: {file_size:,} 字节")
            
            response = input("是否重新下载? (y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("跳过下载")
                success_count += 1
                continue
        
        # 下载文件
        if download_file(url, filepath):
            success_count += 1
            file_size = filepath.stat().st_size
            print(f"文件大小: {file_size:,} 字节")
    
    # 显示下载结果
    print("\n" + "=" * 50)
    print(f"下载完成: {success_count}/{total_count} 个文件成功")
    
    if success_count > 0:
        print(f"\n文件保存在: {package_path.absolute()}")
        print("\n已下载的文件:")
        for file in package_path.glob("python*"):
            if file.is_file():
                size = file.stat().st_size
                print(f"  {file.name} ({size:,} 字节)")
        
        print("\n使用说明:")
        print("Windows:")
        print("  - 运行 .exe 文件进行图形化安装")
        print("Linux:")
        print("  - 解压源码包: tar -xzf Python-*.tgz 或 tar -xf Python-*.tar.xz")
        print("  - 编译安装:")
        print("    cd Python-*")
        print("    ./configure --prefix=/usr/local")
        print("    make && sudo make install")

if __name__ == "__main__":
    main()
