#!/bin/bash

# 下载 Python 3.10 官方离线安装包
# 支持 Windows 和 Linux 平台

PYTHON_VERSION="3.10.16"
PACKAGE_DIR="package"
BASE_URL="https://www.python.org/ftp/python/${PYTHON_VERSION}"

# 创建 package 目录（如果不存在）
mkdir -p "${PACKAGE_DIR}"

echo "开始下载 Python ${PYTHON_VERSION} 离线安装包..."

# Windows 版本
WINDOWS_FILE="python-${PYTHON_VERSION}-amd64.exe"
WINDOWS_URL="${BASE_URL}/${WINDOWS_FILE}"

echo "下载 Windows 版本: ${WINDOWS_FILE}"
if curl -L -o "${PACKAGE_DIR}/${WINDOWS_FILE}" "${WINDOWS_URL}"; then
    echo "✓ Windows 版本下载完成"
else
    echo "✗ Windows 版本下载失败"
fi

# Linux 版本 (源码包，可以在各种 Linux 发行版上编译)
LINUX_FILE="Python-${PYTHON_VERSION}.tgz"
LINUX_URL="${BASE_URL}/${LINUX_FILE}"

echo "下载 Linux 源码包: ${LINUX_FILE}"
if curl -L -o "${PACKAGE_DIR}/${LINUX_FILE}" "${LINUX_URL}"; then
    echo "✓ Linux 源码包下载完成"
else
    echo "✗ Linux 源码包下载失败"
fi

# 也下载 Linux 的预编译版本 (如果需要特定发行版的预编译版本)
# 这里以常见的 x86_64 架构为例

echo "下载完成！文件保存在 ${PACKAGE_DIR} 目录下："
ls -la "${PACKAGE_DIR}"/python* "${PACKAGE_DIR}"/Python* 2>/dev/null || echo "没有找到 Python 安装包文件"

echo ""
echo "使用说明："
echo "Windows: 运行 ${PACKAGE_DIR}/${WINDOWS_FILE} 进行安装"
echo "Linux: 解压 ${PACKAGE_DIR}/${LINUX_FILE} 并编译安装"
echo "  tar -xzf ${PACKAGE_DIR}/${LINUX_FILE}"
echo "  cd Python-${PYTHON_VERSION}"
echo "  ./configure --prefix=/usr/local"
echo "  make && sudo make install"
