#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database migration script: Add task_id field to Extraction table
"""

import sqlite3
import os

def migrate_database():
    """Add task_id field to Extraction table"""
    db_path = 'extraction.db'
    
    if not os.path.exists(db_path):
        print("Database file does not exist, no migration needed")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if task_id field already exists
        cursor.execute("PRAGMA table_info(extractions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'task_id' not in columns:
            print("Adding task_id field to extractions table...")
            cursor.execute("ALTER TABLE extractions ADD COLUMN task_id INTEGER")
            conn.commit()
            print("Task_id field added successfully!")
        else:
            print("Task_id field already exists, no need to add")
        
        conn.close()
        print("Database migration completed")
        
    except Exception as e:
        print("Database migration failed: " + str(e))

if __name__ == "__main__":
    migrate_database()
