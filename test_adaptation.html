<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>屏幕适配测试</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: Arial, sans-serif;
        }

        /* 16:9适配容器 */
        .aspect-ratio-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 3.2:1到16:9的适配包装器 */
        .content-wrapper {
            width: 100vw;
            height: 100vh;
            transform-origin: center center;
            transform: scaleY(0.556);
            position: relative;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
        }

        /* 等比例缩放模式 */
        .content-wrapper.proportional-scale {
            transform: scale(0.556);
        }

        /* 拉伸模式 */
        .content-wrapper.stretch-mode {
            transform: scaleY(0.556) scaleX(1.0);
        }

        .test-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 40px;
            border-radius: 20px;
        }

        .mode-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            color: #333;
        }

        .controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }

        button:hover {
            background: #0056b3;
        }

        button.active {
            background: #28a745;
        }

        .grid {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 100px 100px;
        }

        .corner-markers {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .corner {
            position: absolute;
            width: 50px;
            height: 50px;
            background: red;
            border-radius: 50%;
        }

        .corner.top-left { top: 20px; left: 20px; }
        .corner.top-right { top: 20px; right: 20px; }
        .corner.bottom-left { bottom: 20px; left: 20px; }
        .corner.bottom-right { bottom: 20px; right: 20px; }
    </style>
</head>
<body>
    <div class="aspect-ratio-container">
        <div class="content-wrapper" id="contentWrapper">
            <div class="grid"></div>
            <div class="corner-markers">
                <div class="corner top-left"></div>
                <div class="corner top-right"></div>
                <div class="corner bottom-left"></div>
                <div class="corner bottom-right"></div>
            </div>
            
            <div class="mode-info" id="modeInfo">
                <strong>当前模式：</strong><span id="currentMode">压缩模式</span><br>
                <strong>原始比例：</strong>3.2:1<br>
                <strong>目标比例：</strong>16:9 (≈1.78:1)<br>
                <strong>压缩比例：</strong>0.556
            </div>

            <div class="test-content">
                <h1>屏幕适配测试</h1>
                <p>这是一个3.2:1比例的内容适配到16:9屏幕的测试页面</p>
                <p>观察四个角落的红色圆点和网格线的变化</p>
                <p><strong>快捷键：</strong></p>
                <p>按键 1 - 压缩模式（推荐）</p>
                <p>按键 2 - 等比例模式</p>
                <p>按键 3 - 拉伸模式</p>
            </div>

            <div class="controls">
                <button id="btn1" class="active" onclick="switchMode('compress')">压缩模式</button>
                <button id="btn2" onclick="switchMode('proportional')">等比例模式</button>
                <button id="btn3" onclick="switchMode('stretch')">拉伸模式</button>
            </div>
        </div>
    </div>

    <script>
        let currentAdaptationMode = 'compress';

        function switchMode(mode) {
            const contentWrapper = document.getElementById('contentWrapper');
            const modeInfo = document.getElementById('currentMode');
            
            // 移除所有模式类
            contentWrapper.classList.remove('proportional-scale', 'stretch-mode');
            
            // 移除所有按钮的active类
            document.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
            
            switch(mode) {
                case 'compress':
                    contentWrapper.style.transform = 'scaleY(0.556)';
                    modeInfo.textContent = '压缩模式（垂直压缩）';
                    document.getElementById('btn1').classList.add('active');
                    break;
                    
                case 'proportional':
                    contentWrapper.classList.add('proportional-scale');
                    contentWrapper.style.transform = 'scale(0.556)';
                    modeInfo.textContent = '等比例模式（可能有黑边）';
                    document.getElementById('btn2').classList.add('active');
                    break;
                    
                case 'stretch':
                    contentWrapper.classList.add('stretch-mode');
                    contentWrapper.style.transform = 'scaleY(0.556) scaleX(1.0)';
                    modeInfo.textContent = '拉伸模式（完全填充）';
                    document.getElementById('btn3').classList.add('active');
                    break;
            }
            
            currentAdaptationMode = mode;
            console.log('切换到模式:', mode);
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case '1':
                    switchMode('compress');
                    break;
                case '2':
                    switchMode('proportional');
                    break;
                case '3':
                    switchMode('stretch');
                    break;
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            switchMode('compress');
            console.log('屏幕适配测试页面已加载');
        });
    </script>
</body>
</html>
