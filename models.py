from sqlalchemy import create_engine, Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from sqlalchemy.pool import QueuePool

Base = declarative_base()
engine = create_engine('sqlite:///extraction.db', 
                      poolclass=QueuePool,
                      pool_size=20,
                      max_overflow=30,
                      pool_timeout=60)

class Person(Base):
    __tablename__ = 'persons'

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    department = Column(String, nullable=False)  # 现在表示机构
    position = Column(String, nullable=True)  # 现在表示职务
    created_at = Column(DateTime, default=datetime.now)

class Question(Base):
    __tablename__ = 'questions'

    id = Column(Integer, primary_key=True)
    title = Column(String, nullable=False)
    department = Column(String, nullable=True)  # 题目不再需要部门字段
    created_at = Column(DateTime, default=datetime.now)

class Task(Base):
    __tablename__ = 'tasks'
    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    status = Column(String, default='active')  # active/voided
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class TaskPerson(Base):
    __tablename__ = 'task_persons'
    id = Column(Integer, primary_key=True)
    task_id = Column(Integer, ForeignKey('tasks.id'))
    person_id = Column(Integer, ForeignKey('persons.id'))

class TaskQuestion(Base):
    __tablename__ = 'task_questions'
    id = Column(Integer, primary_key=True)
    task_id = Column(Integer, ForeignKey('tasks.id'))
    question_id = Column(Integer, ForeignKey('questions.id'))

class Extraction(Base):
    __tablename__ = 'extractions'
    
    id = Column(Integer, primary_key=True)
    person_id = Column(Integer, ForeignKey('persons.id'))
    question_id = Column(Integer, ForeignKey('questions.id'))
    task_id = Column(Integer, ForeignKey('tasks.id'), nullable=True)  # 新增字段，支持多任务
    extraction_time = Column(DateTime, default=datetime.now)

Base.metadata.create_all(engine)
SessionLocal = sessionmaker(bind=engine)