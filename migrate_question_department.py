#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database migration script: Make Question department field nullable
"""

import sqlite3
import os

def migrate_database():
    """Make Question department field nullable"""
    db_path = 'extraction.db'
    
    if not os.path.exists(db_path):
        print("Database file does not exist, no migration needed")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Making Question department field nullable...")
        
        # SQLite doesn't support ALTER COLUMN directly, so we need to recreate the table
        # First, create a new table with the updated schema
        cursor.execute("""
            CREATE TABLE questions_new (
                id INTEGER PRIMARY KEY,
                title TEXT NOT NULL,
                department TEXT,
                created_at DATETIME
            )
        """)
        
        # Copy data from old table to new table
        cursor.execute("""
            INSERT INTO questions_new (id, title, department, created_at)
            SELECT id, title, department, created_at FROM questions
        """)
        
        # Drop the old table
        cursor.execute("DROP TABLE questions")
        
        # Rename the new table
        cursor.execute("ALTER TABLE questions_new RENAME TO questions")
        
        conn.commit()
        print("Question department field is now nullable!")
        
        conn.close()
        print("Database migration completed")
        
    except Exception as e:
        print("Database migration failed: " + str(e))

if __name__ == "__main__":
    migrate_database()
